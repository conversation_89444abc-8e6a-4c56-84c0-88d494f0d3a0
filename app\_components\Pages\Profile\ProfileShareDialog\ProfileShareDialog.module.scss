.share_content {
   display: flex;
   flex-direction: column;
   align-items: center;
   padding: 1rem 0;
}

.qr_section {
   display: flex;
   flex-direction: column;
   align-items: center;
   background-color: white;
   padding: 1rem;
   border-radius: 2px;
   margin-bottom: 3rem;
}

.username {
   margin-top: 1rem;
   font-weight: 600;
   color: #000;
   text-transform: uppercase;
   font-size: 1.6rem;
   line-height: 1.2;
}

.share_options {
   display: flex;
   flex-direction: column;
   width: 100%;
   gap: 1rem;
}

.share_button {
   display: flex;
   align-items: center;
   justify-content: center;
   gap: 1rem;
   font-size: 1.4rem;
   padding: 1.2rem;
   width: 100%;

   svg {
      font-size: 1.6rem;
   }
}

@media (min-width: 768px) {
   .share_options {
      flex-direction: row;
   }
}
