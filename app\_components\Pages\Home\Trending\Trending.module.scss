.trending {
   @media (max-width: 1024px) {
      // width: calc(100vw - 12rem);
   }

   &_content {
      display: flex;
      flex-direction: column;
      gap: 3rem;
      margin-top: 1.5rem;

      @media (max-width: 1024px) {
         flex-direction: row;
         overflow: scroll;
         width: 100%;
         width: calc(100vw - 6rem);
      }

      @media (max-width: 768px) {
         gap: 2.5rem;
      }

      @media (max-width: 425px) {
         width: calc(100vw - 4rem);
      }
   }

   &_item {
      display: grid;
      align-items: center;
      gap: 2rem;
      position: relative;
      flex-direction: column;
      gap: 1.5rem;

      &:hover {
         img {
            transform: scale(1.05);
         }

         .info .info_title span {
            background-size: 100% 0.4rem;
         }
      }

      .overlay {
         position: absolute;
         top: 0;
         left: 0;
         width: 100%;
         height: 100%;
         z-index: 1;
      }

      .poster {
         position: relative;
         border-radius: 1rem;
         // border-radius: 0.5rem;
         overflow: hidden;
         height: 18rem;

         @media (max-width: 1024px) {
            width: 35rem;
         }

         @media (max-width: 768px) {
            width: 30rem;
         }

         img {
            object-fit: cover;
            transition: 0.3s;
         }

         .position {
            position: absolute;
            top: 1rem;
            left: 1rem;
            font-size: 1.8rem;
            font-weight: 500;
            background: #1e1e1e;
            color: #fff;
            min-width: 3.5rem;
            min-height: 3.5rem;
            border-radius: 1rem;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
         }
      }

      .info {
         display: flex;
         flex-direction: column;
         align-items: flex-start;
         gap: 0.5rem;
         position: relative;

         &_category {
            position: absolute;
            top: -18.5rem;
            right: 1.2rem;
            z-index: 1;
         }

         &_date {
            font-size: 1.2rem;
            font-size: 1.3rem;
            font-weight: 500;
            color: #a8a8a8;
            opacity: 0.7;
         }

         &_title {
            color: var(--color-primary);
            font-size: 1.5rem;
            font-weight: 500;
            line-height: 1.5;
            display: -webkit-box;
            line-clamp: 2;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;

            span {
               display: inline;
               background-image: var(--background-gradient);
               background-size: 0% 4px;
               background-position: 0 90%;
               background-repeat: no-repeat;
               transition-property: background-size;
               transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
               transition-duration: 500ms;
            }
         }

         &_tags {
            display: flex;
            gap: 1rem;
            font-size: 1.3rem;
            font-size: 1.4rem;
            color: var(--text-secondary);
         }
      }
   }
}
