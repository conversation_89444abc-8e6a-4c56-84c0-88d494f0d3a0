.related_posts {
   margin-top: 5rem;
   margin-bottom: 2rem;
   max-width: 1200px;
   width: 100%;
   margin-left: auto;
   margin-right: auto;
   padding: 0 2rem;

   @media (max-width: 768px) {
      margin-top: 4rem;
      padding: 0 1rem;
   }
}

.divider {
   width: 100%;
   height: 1px;
   background-color: #292929;
   margin-bottom: 3rem;
}

.posts_grid {
   display: grid;
   grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
   gap: 2rem;
   margin-top: 2rem;

   @media (max-width: 1024px) {
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
   }

   @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 2rem;
   }
}

.loading_state {
   display: grid;
   grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
   gap: 2rem;
   margin-top: 2rem;

   @media (max-width: 1024px) {
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
   }

   @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 2rem;
   }
}

.skeleton_card {
   display: grid;
   gap: 2rem;
   align-items: center;
   margin: 1.5rem 0;

   @media (max-width: 768px) {
      grid-template-columns: 1fr;
      grid-template-rows: auto 1fr;
      gap: 1rem;
   }
}

.skeleton_poster {
   position: relative;
   height: 18rem;
   width: 32.5rem;
   border-radius: 1rem;
   overflow: hidden;

   @media (max-width: 1024px) {
      width: 35rem;
   }

   @media (max-width: 768px) {
      width: 100%;
      height: 20rem;
   }

   @media (max-width: 425px) {
      height: 15rem;
   }
}

.skeleton_info {
   display: flex;
   flex-direction: column;
   gap: 0.5rem;
   align-items: flex-start;
}

.skeleton_category {
   border-radius: 5rem;
}

.error {
   margin-top: 2rem;
   padding: 2rem;
   background-color: rgba(239, 68, 68, 0.1);
   border: 1px solid rgba(239, 68, 68, 0.2);
   border-radius: 1rem;
   text-align: center;

   p {
      color: #ef4444;
      font-size: 1.4rem;
      margin: 0;
   }
}

// Reset ItemCard margins for grid layout
.posts_grid .post_item {
   :global(.item) {
      margin: 0;
   }
}
